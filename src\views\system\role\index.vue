<script setup lang="tsx">
import { ref } from 'vue';
import { N<PERSON>utton, NDropdown, NPopconfirm } from 'naive-ui';
import { jsonClone } from '@sa/utils';
import { roleApi } from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import SwitchStatus from '@/components/custom/switch-status.vue';
import OperateDrawer from './modules/OperateDrawer.vue';
import SearchBar from './modules/SearchBar.vue';
import PermitDrawer, { type OperateType as PermitOperateType } from './modules/PermitDrawer.vue';

const appStore = useAppStore();

const permitOperateOptions = [
  { label: '菜单权限', key: 'menu' },
  { label: '接口权限', key: 'api' }
];

const { columns, columnChecks, data, getData, getDataByPage, loading, pagination, searchParams, resetSearchParams } =
  useTable({
    apiFn: roleApi.list,
    showTotal: true,
    apiParams: {
      _page: 1,
      _limit: 10,
      _sort: 'id',
      _order: 'asc',
      status: null,
      name: null,
      code: null
    },
    columns: () => [
      {
        type: 'selection',
        align: 'center',
        width: 48
      },
      {
        key: 'name',
        title: '名称',
        align: 'left'
      },
      {
        key: 'code',
        title: '编码',
        align: 'left'
      },
      {
        key: 'summary',
        title: '描述',
        align: 'left',
        minWidth: 200,
        ellipsis: true
      },
      {
        key: 'status',
        title: '状态',
        align: 'center',
        render: row => <SwitchStatus v-model:value={row.status} apiFn={status => roleApi.save({ ...row, status })} />
      },
      {
        key: 'operate',
        title: '操作',
        align: 'center',
        fixed: 'right',
        width: 150,
        render: row => (
          <div class="flex flex-center gap-12px">
            <NDropdown
              options={permitOperateOptions}
              size="small"
              onSelect={(key: string) => handlePermit(key, row.id)}
            >
              <NButton type="primary" text size="small">
                权限
              </NButton>
            </NDropdown>
            <NButton type="primary" text size="small" onClick={() => edit(row.id)}>
              编辑
            </NButton>
            <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
              {{
                default: () => '确定删除吗？',
                trigger: () => (
                  <NButton type="error" text size="small">
                    删除
                  </NButton>
                )
              }}
            </NPopconfirm>
          </div>
        )
      }
    ]
  });

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  const { error } = await roleApi.batchDel(checkedRowKeys.value);

  if (!error) {
    onBatchDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleDelete(id: number) {
  const { error } = await roleApi.del(id);

  if (!error) {
    onDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

function edit(id: number) {
  handleEdit(id);
}

// 权限
const permitOperateType = ref<PermitOperateType>('menu');
const permitVisible = ref(false);

function handlePermit(key: string, id: number) {
  const findItem = data.value.find(item => item.id === id) || null;
  editingData.value = jsonClone(findItem);

  permitOperateType.value = key as PermitOperateType;
  permitVisible.value = true;
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-12px overflow-hidden lt-sm:overflow-auto">
    <SearchBar v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        striped
        :bordered="false"
        :row-key="row => row.id"
        :pagination="pagination"
        class="b-t-1px sm:h-full b-auto"
      />
      <OperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
      <PermitDrawer
        v-model:visible="permitVisible"
        :operate-type="permitOperateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
