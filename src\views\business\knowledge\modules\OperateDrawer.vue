<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { knowledgeApi } from '@/service/api';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useDict } from '@/hooks/business/dict';
import WangEditor from '@/components/custom/wang-editor.vue';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Business.Knowledge | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增知识',
    edit: '编辑知识'
  };
  return titles[props.operateType];
});

type Model = Pick<
  Api.Business.Knowledge,
  'title' | 'industry' | 'summary' | 'content' | 'tags' | 'cover' | 'files' | 'order' | 'flag' | 'status'
>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    title: '',
    industry: null,
    summary: '',
    content: '',
    tags: [],
    cover: '',
    files: [],
    order: 0,
    flag: [],
    status: true
  };
}

type RuleKey = Extract<keyof Model, 'title' | 'industry'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  title: defaultRequiredRule,
  industry: defaultRequiredRule
};

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();
  // request
  const { error } =
    props.operateType === 'edit'
      ? await knowledgeApi.save({ id: props.rowData!.id, ...model.value })
      : await knowledgeApi.add(model.value);

  if (!error) {
    window.$message?.success(`${title.value}成功`);
    closeDrawer();
    emit('submitted');
  } else {
    window.$message?.error(`${title.value}失败`);
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="800">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
        <NTabs type="card" placement="left" animated>
          <NTabPane name="base" tab="基础信息">
            <NFormItem label="标题" path="title">
              <NInput v-model:value="model.title" placeholder="请输入标题" clearable />
            </NFormItem>
            <NFormItem label="行业" path="industry">
              <NSelect
                v-model:value="model.industry"
                placeholder="请选择行业"
                :options="useDict('number').items('Industry')"
              />
            </NFormItem>
            <NFormItem label="封面" path="cover">
              <UploadCover v-model:value="model.cover" :options="{ maxWidth: 400, maxHeight: 200 }" />
            </NFormItem>
            <NFormItem label="摘要" path="summary">
              <NInput v-model:value="model.summary" placeholder="请输入摘要" type="textarea" clearable />
            </NFormItem>
            <NFormItem label="标签" path="tags">
              <NDynamicTags v-model:value="model.tags" />
            </NFormItem>
          </NTabPane>
          <NTabPane name="content" tab="编辑正文">
            <WangEditor v-model:value="model.content" />
          </NTabPane>
          <NTabPane name="files" tab="上传文档">
            <UploadFiles
              v-model:value="model.files"
              :max="1"
              accept="application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            />
          </NTabPane>
          <NTabPane name="more" tab="更多">
            <NFormItem label="标志" path="flag">
              <NSelect
                v-model:value="model.flag"
                placeholder="请选择标志"
                :options="useDict('number').items('Flag')"
                multiple
                clearable
              />
            </NFormItem>
            <NFormItem label="排序" path="order">
              <NInputNumber v-model:value="model.order" placeholder="请输入排序" clearable />
            </NFormItem>
            <NFormItem label="状态" path="status">
              <NSwitch v-model:value="model.status" />
            </NFormItem>
          </NTabPane>
        </NTabs>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" @click="handleSubmit">确定</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
