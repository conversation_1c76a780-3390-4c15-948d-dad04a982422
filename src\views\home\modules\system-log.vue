<script setup lang="ts">
import { onMounted, ref } from 'vue';
import dayjs from 'dayjs';
import { logApi } from '@/service/api';

defineOptions({
  name: 'SystemLog'
});

const logs = ref<Api.System.Log[]>([]);

onMounted(() => {
  logApi
    .list({
      _page: 1,
      _limit: 4,
      _sort: 'id',
      _order: 'desc'
    })
    .then(res => {
      logs.value = res.data?.records || [];
    });
});
</script>

<template>
  <NCard title="操作日志" :bordered="false" size="small" segmented class="card-wrapper">
    <template #header-extra>
      <span class="cursor-pointer text-primary hover-opacity-80" @click="$router.push('/system/log')">更多日志</span>
    </template>
    <NList>
      <NListItem v-for="item in logs" :key="item.id">
        <template #prefix>
          <div class="h-48px w-48px flex items-center justify-center b-rd-50% bg-primary text-12px text-white">
            {{ item.method }}
          </div>
        </template>
        <NThing
          :title="`${item.path}`"
          :description="`IP：${item.clientIp}，CreatedAt：${dayjs(item.createdAt).format('YYYY-MM-DD HH:mm:ss')}`"
        />
      </NListItem>
    </NList>
  </NCard>
</template>

<style scoped></style>
