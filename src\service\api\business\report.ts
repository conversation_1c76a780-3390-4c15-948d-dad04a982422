import { request } from '@/service/request';

export const reportApi = {
  // 获取报告列表
  list: (params: Api.Business.ReportSearchParams) =>
    request<Api.Business.ReportList>({
      url: '/business/reports',
      params
    }),

  // 创建报告
  add: (data: Api.Business.ReportCreateParams) =>
    request<null>({
      url: '/business/reports',
      method: 'POST',
      data
    }),

  // 更新报告
  save: (data: Api.Business.ReportUpdateParams) =>
    request<null>({
      url: `/business/reports/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除报告
  del: (id: number) =>
    request<null>({
      url: `/business/reports/${id}`,
      method: 'DELETE'
    }),

  // 批量删除报告
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/business/reports',
      method: 'DELETE',
      data: { ids }
    })
};
