import { request } from '@/service/request';

export const talentApi = {
  // 获取人才列表
  list: (params: Api.Business.TalentSearchParams) =>
    request<Api.Business.TalentList>({
      url: '/business/talents',
      params
    }),

  // 创建人才
  add: (data: Api.Business.TalentCreateParams) =>
    request<null>({
      url: '/business/talents',
      method: 'POST',
      data
    }),

  // 更新人才
  save: (data: Api.Business.TalentUpdateParams) =>
    request<null>({
      url: `/business/talents/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除人才
  del: (id: number) =>
    request<null>({
      url: `/business/talents/${id}`,
      method: 'DELETE'
    }),

  // 批量删除人才
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/business/talents',
      method: 'DELETE',
      data: { ids }
    })
};
