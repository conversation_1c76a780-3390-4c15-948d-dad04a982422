<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useCascaderAreaData } from '@vant/area-data';
import { companyApi } from '@/service/api';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useDict } from '@/hooks/business/dict';
import WangEditor from '@/components/custom/wang-editor.vue';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Business.Company | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增公司',
    edit: '编辑公司'
  };
  return titles[props.operateType];
});

const areaData = useCascaderAreaData();

type Model = Pick<
  Api.Business.Company,
  | 'name'
  | 'industry'
  | 'type'
  | 'size'
  | 'contact'
  | 'area'
  | 'address'
  | 'phone'
  | 'email'
  | 'website'
  | 'logo'
  | 'summary'
  | 'detail'
  | 'albums'
  | 'tags'
  | 'order'
  | 'flag'
  | 'status'
>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    name: '',
    industry: null,
    type: null,
    size: null,
    contact: '',
    area: null,
    address: '',
    phone: '',
    email: '',
    website: '',
    logo: '',
    summary: '',
    detail: '',
    albums: [],
    tags: [],
    order: 0,
    flag: [],
    status: true
  };
}

type RuleKey = Extract<keyof Model, 'name'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  name: defaultRequiredRule
};

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();
  // request
  const { error } =
    props.operateType === 'edit'
      ? await companyApi.save({ id: props.rowData!.id, ...model.value })
      : await companyApi.add(model.value);

  if (!error) {
    window.$message?.success(`${title.value}成功`);
    closeDrawer();
    emit('submitted');
  } else {
    window.$message?.error(`${title.value}失败`);
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="800">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
        <NTabs type="card" placement="left" animated>
          <NTabPane name="base" tab="基础信息">
            <NGrid :cols="2" :x-gap="12">
              <NFormItemGi label="名称" path="name">
                <NInput v-model:value="model.name" placeholder="请输入名称" clearable />
              </NFormItemGi>
              <NFormItemGi label="行业" path="industry">
                <NSelect
                  v-model:value="model.industry"
                  placeholder="请选择行业"
                  :options="useDict('number').items('Industry')"
                />
              </NFormItemGi>
              <NFormItemGi label="类型" path="type">
                <NSelect
                  v-model:value="model.type"
                  placeholder="请选择类型"
                  :options="useDict('number').items('CompanyType')"
                />
              </NFormItemGi>
              <NFormItemGi label="规模" path="size">
                <NSelect
                  v-model:value="model.size"
                  placeholder="请选择规模"
                  :options="useDict('number').items('CompanySize')"
                />
              </NFormItemGi>
              <NFormItemGi label="联系人" path="contact">
                <NInput v-model:value="model.contact" placeholder="请输入联系人" clearable />
              </NFormItemGi>
              <NFormItemGi label="联系电话" path="phone">
                <NInput v-model:value="model.phone" placeholder="请输入联系电话" clearable>
                  <template #prefix>
                    <span class="text-gray">+86</span>
                  </template>
                </NInput>
              </NFormItemGi>
              <NFormItemGi label="邮箱" path="email">
                <NInput v-model:value="model.email" placeholder="请输入邮箱" clearable />
              </NFormItemGi>
              <NFormItemGi label="公司官网" path="website">
                <NInput v-model:value="model.website" placeholder="请输入公司官网" clearable />
              </NFormItemGi>
            </NGrid>
            <NFormItem label="Logo" path="logo">
              <UploadCover v-model:value="model.logo" :options="{ maxWidth: 200, maxHeight: 200 }" />
            </NFormItem>
            <NFormItem label="所在地区" path="area">
              <NCascader v-model:value="model.area" placeholder="请选择省市区" label-field="text" :options="areaData" />
            </NFormItem>
            <NFormItem label="详细地址" path="address">
              <NInput v-model:value="model.address" placeholder="请输入详细地址" clearable />
            </NFormItem>
            <NFormItem label="公司简介" path="summary">
              <NInput v-model:value="model.summary" placeholder="请输入公司简介" type="textarea" clearable />
            </NFormItem>
            <NFormItem label="标签" path="tags">
              <NDynamicTags v-model:value="model.tags" />
            </NFormItem>
          </NTabPane>
          <NTabPane name="albums" tab="相册管理">
            <UploadFiles v-model:value="model.albums" accept="image/*" />
          </NTabPane>
          <NTabPane name="detail" tab="编辑详情">
            <WangEditor v-model:value="model.detail" />
          </NTabPane>
          <NTabPane name="more" tab="更多">
            <NFormItem label="标志" path="flag">
              <NSelect
                v-model:value="model.flag"
                placeholder="请选择标志"
                :options="useDict('number').items('Flag')"
                multiple
                clearable
              />
            </NFormItem>
            <NFormItem label="排序" path="order">
              <NInputNumber v-model:value="model.order" placeholder="请输入排序" clearable />
            </NFormItem>
            <NFormItem label="状态" path="status">
              <NSwitch v-model:value="model.status" />
            </NFormItem>
          </NTabPane>
        </NTabs>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" @click="handleSubmit">确定</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
