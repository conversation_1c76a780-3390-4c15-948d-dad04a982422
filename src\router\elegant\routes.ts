/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      i18nKey: 'route.403',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      i18nKey: 'route.404',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      i18nKey: 'route.500',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'business',
    path: '/business',
    component: 'layout.base',
    meta: {
      title: 'business',
      i18nKey: 'route.business'
    },
    children: [
      {
        name: 'business_app',
        path: '/business/app',
        component: 'view.business_app',
        meta: {
          title: 'business_app',
          i18nKey: 'route.business_app'
        }
      },
      {
        name: 'business_company',
        path: '/business/company',
        component: 'view.business_company',
        meta: {
          title: 'business_company',
          i18nKey: 'route.business_company'
        }
      },
      {
        name: 'business_expert',
        path: '/business/expert',
        component: 'view.business_expert',
        meta: {
          title: 'business_expert',
          i18nKey: 'route.business_expert'
        }
      },
      {
        name: 'business_job',
        path: '/business/job',
        component: 'view.business_job',
        meta: {
          title: 'business_job',
          i18nKey: 'route.business_job'
        }
      },
      {
        name: 'business_knowledge',
        path: '/business/knowledge',
        component: 'view.business_knowledge',
        meta: {
          title: 'business_knowledge',
          i18nKey: 'route.business_knowledge'
        }
      },
      {
        name: 'business_product',
        path: '/business/product',
        component: 'view.business_product',
        meta: {
          title: 'business_product',
          i18nKey: 'route.business_product'
        }
      },
      {
        name: 'business_report',
        path: '/business/report',
        component: 'view.business_report',
        meta: {
          title: 'business_report',
          i18nKey: 'route.business_report'
        }
      },
      {
        name: 'business_talent',
        path: '/business/talent',
        component: 'view.business_talent',
        meta: {
          title: 'business_talent',
          i18nKey: 'route.business_talent'
        }
      }
    ]
  },
  {
    name: 'cms',
    path: '/cms',
    component: 'layout.base',
    meta: {
      title: 'cms',
      i18nKey: 'route.cms'
    },
    children: [
      {
        name: 'cms_meta',
        path: '/cms/meta',
        component: 'view.cms_meta',
        meta: {
          title: 'cms_meta',
          i18nKey: 'route.cms_meta'
        }
      },
      {
        name: 'cms_post',
        path: '/cms/post',
        component: 'view.cms_post',
        meta: {
          title: 'cms_post',
          i18nKey: 'route.cms_post'
        }
      }
    ]
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: 'home',
      i18nKey: 'route.home',
      icon: 'mdi:monitor-dashboard',
      order: 1
    }
  },
  {
    name: 'iframe-page',
    path: '/iframe-page/:url',
    component: 'layout.base$view.iframe-page',
    props: true,
    meta: {
      title: 'iframe-page',
      i18nKey: 'route.iframe-page',
      constant: true,
      hideInMenu: true,
      keepAlive: true
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      i18nKey: 'route.login',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'system',
    path: '/system',
    component: 'layout.base',
    meta: {
      title: 'system',
      i18nKey: 'route.system'
    },
    children: [
      {
        name: 'system_api',
        path: '/system/api',
        component: 'view.system_api',
        meta: {
          title: 'system_api',
          i18nKey: 'route.system_api'
        }
      },
      {
        name: 'system_config',
        path: '/system/config',
        component: 'view.system_config',
        meta: {
          title: 'system_config',
          i18nKey: 'route.system_config'
        }
      },
      {
        name: 'system_dept',
        path: '/system/dept',
        component: 'view.system_dept',
        meta: {
          title: 'system_dept',
          i18nKey: 'route.system_dept'
        }
      },
      {
        name: 'system_dict',
        path: '/system/dict',
        component: 'view.system_dict',
        meta: {
          title: 'system_dict',
          i18nKey: 'route.system_dict'
        }
      },
      {
        name: 'system_log',
        path: '/system/log',
        component: 'view.system_log',
        meta: {
          title: 'system_log',
          i18nKey: 'route.system_log'
        }
      },
      {
        name: 'system_menu',
        path: '/system/menu',
        component: 'view.system_menu',
        meta: {
          title: 'system_menu',
          i18nKey: 'route.system_menu'
        }
      },
      {
        name: 'system_role',
        path: '/system/role',
        component: 'view.system_role',
        meta: {
          title: 'system_role',
          i18nKey: 'route.system_role'
        }
      },
      {
        name: 'system_user',
        path: '/system/user',
        component: 'view.system_user',
        meta: {
          title: 'system_user',
          i18nKey: 'route.system_user'
        }
      }
    ]
  }
];
