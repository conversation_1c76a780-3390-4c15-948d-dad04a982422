import { request } from '@/service/request';

export const productApi = {
  // 获取产品列表
  list: (params: Api.Business.ProductSearchParams) =>
    request<Api.Business.ProductList>({
      url: '/business/products',
      params
    }),

  // 创建产品
  add: (data: Api.Business.ProductCreateParams) =>
    request<null>({
      url: '/business/products',
      method: 'POST',
      data
    }),

  // 更新产品
  save: (data: Api.Business.ProductUpdateParams) =>
    request<null>({
      url: `/business/products/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除产品
  del: (id: number) =>
    request<null>({
      url: `/business/products/${id}`,
      method: 'DELETE'
    }),

  // 批量删除产品
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/business/products',
      method: 'DELETE',
      data: { ids }
    })
};
