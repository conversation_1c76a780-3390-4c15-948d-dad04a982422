import { request } from '@/service/request';

export const companyApi = {
  // 获取公司列表
  list: (params: Api.Business.CompanySearchParams) =>
    request<Api.Business.CompanyList>({
      url: '/business/companies',
      params
    }),

  // 创建公司
  add: (data: Api.Business.CompanyCreateParams) =>
    request<null>({
      url: '/business/companies',
      method: 'POST',
      data
    }),

  // 更新公司
  save: (data: Api.Business.CompanyUpdateParams) =>
    request<null>({
      url: `/business/companies/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除公司
  del: (id: number) =>
    request<null>({
      url: `/business/companies/${id}`,
      method: 'DELETE'
    }),

  // 批量删除公司
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/business/companies',
      method: 'DELETE',
      data: { ids }
    })
};
