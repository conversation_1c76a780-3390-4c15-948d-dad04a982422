<script setup lang="ts">
import { computed, ref } from 'vue';
import { dictApi } from '@/service/api';
import { $t } from '@/locales';
import DictModal from './DictModal.vue';

const props = defineProps<{
  dicts: Api.System.Dict[];
}>();

interface Emits {
  (e: 'search'): void;
}
const emit = defineEmits<Emits>();

const current = defineModel<number | null | undefined>('value', { required: true });

const operateOptions = [
  {
    label: $t('common.edit'),
    key: 'edit'
  },
  {
    label: $t('common.delete'),
    key: 'delete',
    props: {
      style: {
        color: 'rgb(var(--error-color))'
      }
    }
  }
];

// 根据字典过滤选项
function filterByDict(index: number) {
  current.value = index;
}

const operateType = ref<NaiveUI.TableOperateType>('add');
const modalVisible = ref(false);
const editingData = ref<Api.System.Dict | null>(null);

function handleAdd() {
  operateType.value = 'add';
  modalVisible.value = true;
}

function handleEdit(dict: Api.System.Dict) {
  operateType.value = 'edit';
  modalVisible.value = true;
  editingData.value = dict;
}

async function handleDelete(id: number) {
  window.$dialog?.create({
    title: $t('common.warning'),
    content: $t('common.confirmDelete'),
    positiveText: $t('common.confirm'),
    negativeText: $t('common.cancel'),
    onPositiveClick: async () => {
      const { error } = await dictApi.del(id);
      if (!error) {
        emit('search');
        window.$message?.success($t('common.deleteSuccess'));
      } else {
        window.$message?.error(error.message);
      }
    }
  });
}

function handleOperate(key: string, dict: Api.System.Dict) {
  if (key === 'edit') {
    handleEdit(dict);
  } else if (key === 'delete') {
    handleDelete(dict.id);
  }
}

const pattern = ref('');
const filteredDicts = computed(() => {
  return props.dicts.filter(dict => dict.name.includes(pattern.value) || dict.code.includes(pattern.value));
});
</script>

<template>
  <NCard :title="$route.meta.title" :bordered="false" size="small" class="h-full">
    <template #header-extra>
      <NButton :focusable="false" quaternary @click="handleAdd">
        <icon-ph-plus class="text-icon" />
      </NButton>
    </template>
    <NInput v-model:value="pattern" placeholder="搜索" />
    <NScrollbar class="m-t-12px">
      <NList v-if="filteredDicts.length > 0" :show-divider="false" hoverable clickable>
        <NListItem
          v-for="(dict, index) in filteredDicts"
          :key="`dict-${index}`"
          :class="{ active: index === current }"
          @click="filterByDict(index)"
        >
          <template #suffix>
            <NDropdown :options="operateOptions" size="small" @select="(key: string) => handleOperate(key, dict)">
              <NButton size="tiny" :focusable="false" quaternary>
                <icon-ph-dots-three class="text-icon" />
              </NButton>
            </NDropdown>
          </template>
          <div class="flex items-center gap-x-6px">
            <span class="select-none">{{ dict.name }}</span>
            <span class="select-none text-gray-300">-</span>
            <span :class="dict.status ? '' : 'line-through'">{{ dict.code }}</span>
          </div>
        </NListItem>
      </NList>
      <NEmpty v-else description="无数据" />
    </NScrollbar>
    <DictModal
      v-model:visible="modalVisible"
      :operate-type="operateType"
      :row-data="editingData"
      @submitted="emit('search')"
    />
  </NCard>
</template>

<style lang="scss" scoped>
:deep(.n-card__content) {
  display: flex;
  flex-direction: column;
  height: calc(100% - 50px);
}
.n-list-item {
  margin-bottom: 8px;
  padding: 8px 16px !important;
  &.active {
    color: rgb(var(--primary-color));
    background-color: rgb(var(--primary-500-color) / 0.1);
  }
}
</style>
