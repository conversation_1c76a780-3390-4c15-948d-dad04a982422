import { request } from '@/service/request';

export const knowledgeApi = {
  // 获取知识列表
  list: (params: Api.Business.KnowledgeSearchParams) =>
    request<Api.Business.KnowledgeList>({
      url: '/business/knowledges',
      params
    }),

  // 创建知识
  add: (data: Api.Business.KnowledgeCreateParams) =>
    request<null>({
      url: '/business/knowledges',
      method: 'POST',
      data
    }),

  // 更新知识
  save: (data: Api.Business.KnowledgeUpdateParams) =>
    request<null>({
      url: `/business/knowledges/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除知识
  del: (id: number) =>
    request<null>({
      url: `/business/knowledges/${id}`,
      method: 'DELETE'
    }),

  // 批量删除知识
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/business/knowledges',
      method: 'DELETE',
      data: { ids }
    })
};
