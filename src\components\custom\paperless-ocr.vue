<script setup lang="ts">
import { ref } from 'vue';
import axios from 'axios';

defineOptions({
  name: 'PaperlessOcr'
});

const props = defineProps<{
  fileUrl: string;
}>();

const loading = ref(0)

const minioUrl = import.meta.env.VITE_IMAGE_BASE_URL
const paperlessUrl = import.meta.env.VITE_PAPERLESS_URL
const paperlessToken = import.meta.env.VITE_PAPERLESS_TOKEN

function handleOcr() {

}

async function downloadFile(url: string) {
  const res = await axios.get(url, {
    responseType: 'blob'
  });
  return res.data;
}

function uploadDoc() {
  axios.post({
    url: paperlessUrl + '/documents/post_document/',
    headers: {
      Authorization: `Token ${paperlessToken}`
    },
    {
      document: file
    }
  })
}
</script>

<template>
  <NButton class="mt-12px" type="primary" :loading="loading" @click="handleOcr">
    一键OCR
  </NButton>
</template>

<style scoped></style>
