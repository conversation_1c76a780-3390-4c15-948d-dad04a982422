import { request } from '@/service/request';

export const roleApi = {
  // 获取角色列表
  list: (params: Api.System.RoleSearchParams) =>
    request<Api.System.RoleList>({
      url: '/system/roles',
      params
    }),

  // 创建角色
  add: (data: Api.System.RoleCreateParams) =>
    request<null>({
      url: '/system/roles',
      method: 'POST',
      data
    }),

  // 更新角色
  save: (data: Api.System.RoleUpdateParams) =>
    request<null>({
      url: `/system/roles/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除角色
  del: (id: number) =>
    request<null>({
      url: `/system/roles/${id}`,
      method: 'DELETE'
    }),

  // 批量删除角色
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/system/roles',
      method: 'DELETE',
      data: { ids }
    }),

  // 配置角色权限
  permit: (data: Api.System.RolePermitParams) =>
    request<null>({
      url: `/system/roles/${data.id}`,
      method: 'PATCH',
      data
    })
};
