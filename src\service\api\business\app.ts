import { request } from '@/service/request';

export const appApi = {
  // 获取应用列表
  list: (params: Api.Business.AppSearchParams) =>
    request<Api.Business.AppList>({
      url: '/business/apps',
      params
    }),

  // 创建应用
  add: (data: Api.Business.AppCreateParams) =>
    request<null>({
      url: '/business/apps',
      method: 'POST',
      data
    }),

  // 更新应用
  save: (data: Api.Business.AppUpdateParams) =>
    request<null>({
      url: `/business/apps/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除应用
  del: (id: number) =>
    request<null>({
      url: `/business/apps/${id}`,
      method: 'DELETE'
    }),

  // 批量删除应用
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/business/apps',
      method: 'DELETE',
      data: { ids }
    })
};
