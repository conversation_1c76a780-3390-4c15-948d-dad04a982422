<script lang="ts" setup>
import { computed } from 'vue';
import type { UploadCustomRequestOptions, UploadFileInfo } from 'naive-ui';
import { uploadApi } from '@/service/api';
import { type CompressorOptions, useCompressor } from '@/hooks/business/compressor';

defineOptions({ name: 'UploadCover' });

interface Props {
  options?: CompressorOptions;
}

const props = defineProps<Props>();

const value = defineModel<string>('value', {
  default: ''
});

const fileList = computed<UploadFileInfo[]>(() => {
  return value.value
    ? [
        {
          id: new Date().getTime().toString(),
          name: value.value,
          url: value.value.startsWith('http') ? value.value : import.meta.env.VITE_IMAGE_BASE_URL + value.value,
          status: 'finished'
        }
      ]
    : [];
});

async function handleBeforeUpload({ file }: { file: UploadFileInfo }) {
  try {
    file.file = await useCompressor(file.file as File, props.options);
  } catch {}
}

async function handleUpload({ file, onFinish, onError, onProgress }: UploadCustomRequestOptions) {
  const formData = new FormData();
  formData.append('file', file.file as File);

  const { data, error } = await uploadApi.image(formData, onProgress);
  if (!error) {
    value.value = data.fileName;
    window.$message?.success('上传成功');
    onFinish();
  } else {
    window.$message?.error('上传失败');
    onError();
  }
}

async function handleRemove() {
  const { error } = await uploadApi.remove(value.value);
  if (!error) {
    value.value = '';
    window.$message?.success('删除成功');
  } else {
    window.$message?.error('删除失败');
  }
}
</script>

<template>
  <NUpload
    list-type="image-card"
    :default-file-list="fileList"
    :max="1"
    :custom-request="handleUpload"
    @before-upload="handleBeforeUpload"
    @remove="handleRemove"
  />
</template>

<style scoped></style>
