import { request } from '@/service/request';

export const expertApi = {
  // 获取专家列表
  list: (params: Api.Business.ExpertSearchParams) =>
    request<Api.Business.ExpertList>({
      url: '/business/experts',
      params
    }),

  // 创建专家
  add: (data: Api.Business.ExpertCreateParams) =>
    request<null>({
      url: '/business/experts',
      method: 'POST',
      data
    }),

  // 更新专家
  save: (data: Api.Business.ExpertUpdateParams) =>
    request<null>({
      url: `/business/experts/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除专家
  del: (id: number) =>
    request<null>({
      url: `/business/experts/${id}`,
      method: 'DELETE'
    }),

  // 批量删除专家
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/business/experts',
      method: 'DELETE',
      data: { ids }
    })
};
