<script setup lang="tsx">
import type { Ref } from 'vue';
import { ref, watch } from 'vue';
import { NButton, NPopconfirm, NTag } from 'naive-ui';
import { jsonClone } from '@sa/utils';
import { menuApi } from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { useDict } from '@/hooks/business/dict';
import { convertToTree } from '@/utils/common';
import SvgIcon from '@/components/custom/svg-icon.vue';
import SwitchStatus from '@/components/custom/switch-status.vue';
import OperateDrawer from './modules/OperateDrawer.vue';

const appStore = useAppStore();

const { columns, columnChecks, data, getData, getDataByPage, loading, pagination } = useTable({
  apiFn: menuApi.list,
  showTotal: true,
  apiParams: {
    _page: 1,
    _limit: 1000,
    _sort: 'order',
    _order: 'asc'
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'menuName',
      title: '名称',
      align: 'left',
      width: 200
    },
    {
      key: 'menuType',
      title: '类型',
      align: 'left',
      render: row => {
        if (row.menuType === null) return null;

        const option = useDict('number').item('MenuType', row.menuType);
        if (!option) return null;
        return <NTag type={option.type}>{option.label}</NTag>;
      }
    },
    {
      key: 'icon',
      title: '图标',
      align: 'center',
      render: row =>
        row.icon ? (
          <div class="flex-center">
            <SvgIcon icon={row.icon} class="text-18px" />
          </div>
        ) : (
          '-'
        )
    },
    {
      key: 'constant',
      title: '常量',
      align: 'center',
      render: row => (row.constant ? <NTag>是</NTag> : '-')
    },
    {
      key: 'hideInMenu',
      title: '隐藏',
      align: 'center',
      render: row => (row.hideInMenu ? <NTag>是</NTag> : '-')
    },
    {
      key: 'keepAlive',
      title: '缓存',
      align: 'center',
      render: row => (row.keepAlive ? <NTag>是</NTag> : '-')
    },
    {
      key: 'multiTab',
      title: '多标签',
      align: 'center',
      render: row => (row.multiTab ? <NTag>是</NTag> : '-')
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      render: row => <SwitchStatus v-model:value={row.status} apiFn={status => menuApi.save({ ...row, status })} />
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 150,
      render: row => (
        <div class="flex flex-center gap-12px">
          <NButton type="primary" text size="small" onClick={() => addChild(row.id)} disabled={row.menuType !== 1}>
            新增
          </NButton>
          <NButton type="primary" text size="small" onClick={() => edit(row.id)}>
            编辑
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => '确定删除吗？',
              trigger: () => (
                <NButton type="error" text size="small">
                  删除
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

// 树形数据
const treeData = ref<Api.System.Menu[]>([]);
watch(data, nval => {
  treeData.value = nval.length > 0 ? convertToTree(nval) : [];
});

async function handleBatchDelete() {
  const { error } = await menuApi.batchDel(checkedRowKeys.value);

  if (!error) {
    onBatchDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleDelete(id: number) {
  const { error } = await menuApi.del(id);

  if (!error) {
    onDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

function edit(id: number) {
  handleEdit(id);
}

function addChild(id: number) {
  (operateType as Ref<NaiveUI.TableOperateType | 'addChild'>).value = 'addChild';
  const findItem = data.value.find(item => item.id === id) || null;
  editingData.value = jsonClone(findItem);

  drawerVisible.value = true;
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-12px overflow-hidden lt-sm:overflow-auto">
    <NCard :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="treeData"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        striped
        :bordered="false"
        :row-key="row => row.id"
        class="b-t-1px sm:h-full b-auto"
      />
      <OperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        :menus="treeData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
