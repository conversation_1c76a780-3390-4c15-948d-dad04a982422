import { request } from '@/service/request';

export const jobApi = {
  // 获取职位列表
  list: (params: Api.Business.JobSearchParams) =>
    request<Api.Business.JobList>({
      url: '/business/jobs',
      params
    }),

  // 创建职位
  add: (data: Api.Business.JobCreateParams) =>
    request<null>({
      url: '/business/jobs',
      method: 'POST',
      data
    }),

  // 更新职位
  save: (data: Api.Business.JobUpdateParams) =>
    request<null>({
      url: `/business/jobs/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除职位
  del: (id: number) =>
    request<null>({
      url: `/business/jobs/${id}`,
      method: 'DELETE'
    }),

  // 批量删除职位
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/business/jobs',
      method: 'DELETE',
      data: { ids }
    })
};
