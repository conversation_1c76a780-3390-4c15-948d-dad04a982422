<script setup lang="tsx">
import { N<PERSON><PERSON>on, NPopconfirm, NTag } from 'naive-ui';
import { apiApi } from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { useDict } from '@/hooks/business/dict';
import SwitchStatus from '@/components/custom/switch-status.vue';
import OperateDrawer from './modules/OperateDrawer.vue';
import SearchBar from './modules/SearchBar.vue';

const appStore = useAppStore();

const tagMap: Record<number, NaiveUI.ThemeColor> = {
  0: 'primary',
  1: 'error',
  2: 'warning',
  3: 'success',
  4: 'default'
};

const { columns, columnChecks, data, getData, getDataByPage, loading, pagination, searchParams, resetSearchParams } =
  useTable({
    apiFn: apiApi.list,
    showTotal: true,
    apiParams: {
      _page: 1,
      _limit: 10,
      _sort: 'id',
      _order: 'asc',
      status: null,
      path: null,
      method: null
    },
    columns: () => [
      {
        type: 'selection',
        align: 'center',
        width: 48
      },
      {
        key: 'path',
        title: '路径',
        align: 'left',
        minWidth: 100
      },
      {
        key: 'method',
        title: '请求方法',
        align: 'left',
        render: row => {
          if (row.method === null) return null;

          const option = useDict().item('HttpMethod', row.method);
          if (!option) return null;
          return <NTag type={option.type}>{option.label}</NTag>;
        }
      },
      {
        key: 'summary',
        title: '描述',
        align: 'left',
        minWidth: 200,
        ellipsis: true
      },
      {
        key: 'tags',
        title: '标签',
        align: 'left',
        render: row => {
          if (row.tags === null) {
            return null;
          }

          return (
            <div class="flex items-center">
              {row.tags?.map((item: string, index: number) => (
                <>
                  {index > 0 && <icon-ph-arrow-right class="m-x-4px text-12px text-gray-400" />}
                  <NTag type={tagMap[index % 3]} key={index}>
                    {item}
                  </NTag>
                </>
              ))}
            </div>
          );
        }
      },
      {
        key: 'status',
        title: '状态',
        align: 'center',
        render: row => <SwitchStatus v-model:value={row.status} apiFn={status => apiApi.save({ ...row, status })} />
      },
      {
        key: 'operate',
        title: '操作',
        align: 'center',
        fixed: 'right',
        width: 150,
        render: row => (
          <div class="flex flex-center gap-12px">
            <NButton type="primary" text size="small" onClick={() => edit(row.id)}>
              编辑
            </NButton>
            <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
              {{
                default: () => '确定删除吗？',
                trigger: () => (
                  <NButton type="error" text size="small">
                    删除
                  </NButton>
                )
              }}
            </NPopconfirm>
          </div>
        )
      }
    ]
  });

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  const { error } = await apiApi.batchDel(checkedRowKeys.value);

  if (!error) {
    onBatchDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleDelete(id: number) {
  const { error } = await apiApi.del(id);

  if (!error) {
    onDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

function edit(id: number) {
  handleEdit(id);
}

async function handleReset() {
  await apiApi.refresh();
  resetSearchParams();
  await getDataByPage();
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-12px overflow-hidden lt-sm:overflow-auto">
    <SearchBar v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        >
          <template #suffix>
            <NPopconfirm @positive-click="handleReset">
              <template #trigger>
                <NButton>
                  <template #icon>
                    <icon-ph-arrows-clockwise class="text-icon" />
                  </template>
                  重置接口
                </NButton>
              </template>
              确认重置所有接口吗？
            </NPopconfirm>
          </template>
        </TableHeaderOperation>
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        striped
        :bordered="false"
        :row-key="row => row.id"
        :pagination="pagination"
        class="b-t-1px sm:h-full b-auto"
      />
      <OperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
