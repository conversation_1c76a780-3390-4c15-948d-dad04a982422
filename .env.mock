# backend service base url, test environment
VITE_SERVICE_BASE_URL=http://127.0.0.1:9999/api

# other backend service base url, test environment
VITE_OTHER_SERVICE_BASE_URL= `{
  "demo": "http://localhost:9528"
}`

# image base url, test environment
VITE_IMAGE_BASE_URL=/uploads/

# amap weather service key
VITE_AMAP_SERVICE_KEY=e39fa2b9f5d0a9f9f98fc42e392d0e1e

# ragflow
VITE_RAGFLOW_AGENT=https://qdrag.kscss.com/api/v1/agents_openai/dd42d4ce62bf11f0befe0242ac170006/chat/completions
VITE_RAGFLOW_TOKEN=ragflow-k3ODk4OTBjNjJiMzExZjA5NGUyMDI0Mm