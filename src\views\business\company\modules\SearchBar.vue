<script setup lang="ts">
import { useNaiveForm } from '@/hooks/common/form';
import { useDict } from '@/hooks/business/dict';

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();

const model = defineModel<Api.Business.CompanySearchParams>('model', { required: true });

async function reset() {
  await restoreValidation();
  emit('reset');
}

async function search() {
  await validate();
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse>
      <NCollapseItem title="高级搜索" name="search">
        <NForm ref="formRef" :model="model" label-placement="left" :label-width="100">
          <NGrid responsive="screen" item-responsive>
            <NFormItemGi span="24 s:12 m:6" label="名称" path="name">
              <NInput v-model:value="model.name" placeholder="请输入名称" clearable />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="行业" path="industry">
              <NSelect
                v-model:value="model.industry"
                placeholder="请选择行业"
                :options="useDict('number').items('Industry')"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="类型" path="type">
              <NSelect
                v-model:value="model.type"
                placeholder="请选择类型"
                :options="useDict('number').items('CompanyType')"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="标志" path="flag">
              <NSelect
                v-model:value="model.flag"
                placeholder="请选择标志"
                :options="useDict('number').items('Flag')"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="状态" path="status">
              <SelectBool v-model:value="model.status" :options="useDict().items('Status')" clearable />
            </NFormItemGi>
            <NFormItemGi suffix span="24 m:12 m:6">
              <NSpace class="w-full" justify="end">
                <NButton type="primary" @click="search">
                  <template #icon>
                    <icon-ph-magnifying-glass class="text-icon" />
                  </template>
                  搜索
                </NButton>
                <NButton @click="reset">
                  <template #icon>
                    <icon-ph-arrow-counter-clockwise class="text-icon" />
                  </template>
                  重置
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped></style>
