<script setup lang="ts">
import { useNaiveForm } from '@/hooks/common/form';
import { useDict } from '@/hooks/business/dict';

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();

const model = defineModel<Api.Business.ExpertSearchParams>('model', { required: true });

async function reset() {
  await restoreValidation();
  emit('reset');
}

async function search() {
  await validate();
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse>
      <NCollapseItem title="高级搜索" name="search">
        <NForm ref="formRef" :model="model" label-placement="left" :label-width="100">
          <NGrid responsive="screen" item-responsive>
            <NFormItemGi span="24 s:12 m:6" label="姓名" path="name">
              <NInput v-model:value="model.name" placeholder="请输入姓名" clearable />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="性别" path="gender">
              <NSelect
                v-model:value="model.gender"
                :options="useDict().items('Gender')"
                placeholder="请选择性别"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="电话" path="phone">
              <NInput v-model:value="model.phone" placeholder="请输入电话" clearable />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="邮箱" path="email">
              <NInput v-model:value="model.email" placeholder="请输入邮箱" clearable />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="擅长领域" path="type">
              <NSelect
                v-model:value="model.type"
                :options="useDict().items('ExpertType')"
                placeholder="请选择擅长领域"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="标志" path="flag">
              <NSelect
                v-model:value="model.flag"
                placeholder="请选择标志"
                :options="useDict('number').items('Flag')"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="状态" path="status">
              <SelectBool v-model:value="model.status" :options="useDict().items('Status')" clearable />
            </NFormItemGi>
            <NFormItemGi suffix span="24 m:12 m:6">
              <NSpace class="w-full" justify="end">
                <NButton type="primary" @click="search">
                  <template #icon>
                    <icon-ph-magnifying-glass class="text-icon" />
                  </template>
                  搜索
                </NButton>
                <NButton @click="reset">
                  <template #icon>
                    <icon-ph-arrow-counter-clockwise class="text-icon" />
                  </template>
                  重置
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped></style>
