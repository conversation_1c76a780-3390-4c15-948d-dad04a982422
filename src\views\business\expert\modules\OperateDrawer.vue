<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useCascaderAreaData } from '@vant/area-data';
import { expertApi } from '@/service/api';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useDict } from '@/hooks/business/dict';
import WangEditor from '@/components/custom/wang-editor.vue';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Business.Expert | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const areaData = useCascaderAreaData();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增专家',
    edit: '编辑专家'
  };
  return titles[props.operateType];
});

type Model = Pick<
  Api.Business.Expert,
  | 'name'
  | 'gender'
  | 'age'
  | 'edu'
  | 'exp'
  | 'type'
  | 'skills'
  | 'area'
  | 'phone'
  | 'email'
  | 'avatar'
  | 'tags'
  | 'summary'
  | 'detail'
  | 'party'
  | 'order'
  | 'flag'
  | 'status'
>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    name: '',
    gender: null,
    age: null,
    edu: null,
    exp: null,
    type: [],
    skills: [],
    area: '',
    phone: '',
    email: '',
    avatar: '',
    tags: [],
    summary: '',
    detail: '',
    party: false,
    order: 0,
    flag: [],
    status: true
  };
}

type RuleKey = Extract<keyof Model, 'name'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  name: defaultRequiredRule
};

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();
  // request
  const { error } =
    props.operateType === 'edit'
      ? await expertApi.save({ id: props.rowData!.id, ...model.value })
      : await expertApi.add(model.value);

  if (!error) {
    window.$message?.success(`${title.value}成功`);
    closeDrawer();
    emit('submitted');
  } else {
    window.$message?.error(`${title.value}失败`);
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="800">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
        <NTabs type="card" placement="left" animated>
          <NTabPane name="base" tab="基础信息">
            <NGrid :cols="2" :x-gap="12">
              <NFormItemGi label="姓名" path="name">
                <NInput v-model:value="model.name" placeholder="请输入姓名" clearable />
              </NFormItemGi>
              <NFormItemGi label="性别" path="gender">
                <NSelect
                  v-model:value="model.gender"
                  :options="useDict('number').items('Gender')"
                  placeholder="请选择性别"
                  clearable
                />
              </NFormItemGi>
              <NFormItemGi label="年龄" path="age">
                <NInputNumber v-model:value="model.age" :min="0" :step="1" placeholder="请输入年龄" clearable />
              </NFormItemGi>
              <NFormItemGi label="学历" path="edu">
                <NSelect v-model:value="model.edu" placeholder="请选择学历" :options="useDict('number').items('Edu')" />
              </NFormItemGi>
              <NFormItemGi label="工作经验" path="exp">
                <NInputNumber v-model:value="model.exp" :min="0" :step="1" placeholder="请输入工作经验" clearable>
                  <template #suffix>
                    <span class="text-gray">年</span>
                  </template>
                </NInputNumber>
              </NFormItemGi>
              <NFormItemGi label="联系电话" path="phone">
                <NInput v-model:value="model.phone" placeholder="请输入联系电话" clearable />
              </NFormItemGi>
              <NFormItemGi label="邮箱" path="email">
                <NInput v-model:value="model.email" placeholder="请输入邮箱" clearable />
              </NFormItemGi>
              <NFormItemGi label="党员" path="party">
                <NSwitch v-model:value="model.party" />
              </NFormItemGi>
            </NGrid>
            <NFormItem label="头像" path="avatar">
              <UploadCover v-model:value="model.avatar" :options="{ maxWidth: 400, maxHeight: 400 }" />
            </NFormItem>
            <NFormItem label="个人简介" path="summary">
              <NInput v-model:value="model.summary" placeholder="请输入个人简介" type="textarea" clearable />
            </NFormItem>
            <NFormItem label="擅长领域" path="type">
              <NSelect
                v-model:value="model.type"
                placeholder="请选择擅长领域"
                :options="useDict('number').items('ExpertType')"
                multiple
                clearable
              />
            </NFormItem>
            <NFormItem label="常住地" path="area">
              <NCascader
                v-model:value="model.area"
                placeholder="请选择省市区"
                label-field="text"
                :options="areaData"
              />
            </NFormItem>
            <NFormItem label="技能" path="skills">
              <NDynamicTags v-model:value="model.skills" />
            </NFormItem>
            <NFormItem label="标签" path="tags">
              <NDynamicTags v-model:value="model.tags" />
            </NFormItem>
          </NTabPane>
          <NTabPane name="detail" tab="编辑详情">
            <WangEditor v-model:value="model.detail" />
          </NTabPane>
          <NTabPane name="more" tab="更多">
            <NFormItem label="标志" path="flag">
              <NSelect
                v-model:value="model.flag"
                placeholder="请选择标志"
                :options="useDict('number').items('Flag')"
                multiple
                clearable
              />
            </NFormItem>
            <NFormItem label="排序" path="order">
              <NInputNumber v-model:value="model.order" placeholder="请输入排序" clearable />
            </NFormItem>
            <NFormItem label="状态" path="status">
              <NSwitch v-model:value="model.status" />
            </NFormItem>
          </NTabPane>
        </NTabs>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" @click="handleSubmit">确定</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
